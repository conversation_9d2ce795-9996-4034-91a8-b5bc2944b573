"use strict";var u=Object.defineProperty;var P=Object.getOwnPropertyDescriptor;var _=Object.getOwnPropertyNames;var h=Object.prototype.hasOwnProperty;var F=(n,i)=>{for(var s in i)u(n,s,{get:i[s],enumerable:!0})},y=(n,i,s,o)=>{if(i&&typeof i=="object"||typeof i=="function")for(let r of _(i))!h.call(n,r)&&r!==s&&u(n,r,{get:()=>i[r],enumerable:!(o=P(i,r))||o.enumerable});return n};var v=n=>y(u({},"__esModule",{value:!0}),n);var et={};F(et,{BaseDirectory:()=>c,appCacheDir:()=>A,appConfigDir:()=>p,appDataDir:()=>b,appDir:()=>O,appLocalDataDir:()=>w,appLogDir:()=>f,audioDir:()=>T,basename:()=>B,cacheDir:()=>x,configDir:()=>C,dataDir:()=>k,delimiter:()=>J,desktopDir:()=>R,dirname:()=>Y,documentDir:()=>L,downloadDir:()=>j,executableDir:()=>I,extname:()=>Z,fontDir:()=>E,homeDir:()=>U,isAbsolute:()=>tt,join:()=>X,localDataDir:()=>D,logDir:()=>N,normalize:()=>Q,pictureDir:()=>z,publicDir:()=>S,resolve:()=>K,resolveResource:()=>W,resourceDir:()=>V,runtimeDir:()=>H,sep:()=>q,templateDir:()=>$,videoDir:()=>G});module.exports=v(et);function M(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function d(n,i=!1){let s=M(),o=`_${s}`;return Object.defineProperty(window,o,{value:r=>(i&&Reflect.deleteProperty(window,o),n?.(r)),writable:!1,configurable:!0}),s}async function g(n,i={}){return new Promise((s,o)=>{let r=d(a=>{s(a),Reflect.deleteProperty(window,`_${l}`)},!0),l=d(a=>{o(a),Reflect.deleteProperty(window,`_${r}`)},!0);window.__TAURI_IPC__({cmd:n,callback:r,error:l,...i})})}async function e(n){return g("tauri",n)}var c=(t=>(t[t.Audio=1]="Audio",t[t.Cache=2]="Cache",t[t.Config=3]="Config",t[t.Data=4]="Data",t[t.LocalData=5]="LocalData",t[t.Desktop=6]="Desktop",t[t.Document=7]="Document",t[t.Download=8]="Download",t[t.Executable=9]="Executable",t[t.Font=10]="Font",t[t.Home=11]="Home",t[t.Picture=12]="Picture",t[t.Public=13]="Public",t[t.Runtime=14]="Runtime",t[t.Template=15]="Template",t[t.Video=16]="Video",t[t.Resource=17]="Resource",t[t.App=18]="App",t[t.Log=19]="Log",t[t.Temp=20]="Temp",t[t.AppConfig=21]="AppConfig",t[t.AppData=22]="AppData",t[t.AppLocalData=23]="AppLocalData",t[t.AppCache=24]="AppCache",t[t.AppLog=25]="AppLog",t))(c||{});function m(){return navigator.appVersion.includes("Win")}async function O(){return p()}async function p(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:21}})}async function b(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:22}})}async function w(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:23}})}async function A(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:24}})}async function T(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:1}})}async function x(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:2}})}async function C(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:3}})}async function k(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:4}})}async function R(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:6}})}async function L(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:7}})}async function j(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:8}})}async function I(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:9}})}async function E(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:10}})}async function U(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:11}})}async function D(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:5}})}async function z(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:12}})}async function S(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:13}})}async function V(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:17}})}async function W(n){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:n,directory:17}})}async function H(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:14}})}async function $(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:15}})}async function G(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:16}})}async function N(){return f()}async function f(){return e({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:25}})}var q=m()?"\\":"/",J=m()?";":":";async function K(...n){return e({__tauriModule:"Path",message:{cmd:"resolve",paths:n}})}async function Q(n){return e({__tauriModule:"Path",message:{cmd:"normalize",path:n}})}async function X(...n){return e({__tauriModule:"Path",message:{cmd:"join",paths:n}})}async function Y(n){return e({__tauriModule:"Path",message:{cmd:"dirname",path:n}})}async function Z(n){return e({__tauriModule:"Path",message:{cmd:"extname",path:n}})}async function B(n,i){return e({__tauriModule:"Path",message:{cmd:"basename",path:n,ext:i}})}async function tt(n){return e({__tauriModule:"Path",message:{cmd:"isAbsolute",path:n}})}
