"use strict";var u=Object.defineProperty;var v=Object.getOwnPropertyDescriptor;var A=Object.getOwnPropertyNames;var D=Object.prototype.hasOwnProperty;var g=(e,t)=>{for(var n in t)u(e,n,{get:t[n],enumerable:!0})},w=(e,t,n,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of A(t))!D.call(e,o)&&o!==n&&u(e,o,{get:()=>t[o],enumerable:!(i=v(t,o))||i.enumerable});return e};var P=e=>w(u({},"__esModule",{value:!0}),e);var O={};g(O,{checkUpdate:()=>N,installUpdate:()=>I,onUpdaterEvent:()=>p});module.exports=P(O);function T(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function s(e,t=!1){let n=T(),i=`_${n}`;return Object.defineProperty(window,i,{value:o=>(t&&Reflect.deleteProperty(window,i),e?.(o)),writable:!1,configurable:!0}),n}async function m(e,t={}){return new Promise((n,i)=>{let o=s(r=>{n(r),Reflect.deleteProperty(window,`_${a}`)},!0),a=s(r=>{i(r),Reflect.deleteProperty(window,`_${o}`)},!0);window.__TAURI_IPC__({cmd:e,callback:o,error:a,...t})})}async function l(e){return m("tauri",e)}async function f(e,t){return l({__tauriModule:"Event",message:{cmd:"unlisten",event:e,eventId:t}})}async function _(e,t,n){await l({__tauriModule:"Event",message:{cmd:"emit",event:e,windowLabel:t,payload:n}})}async function d(e,t,n){return l({__tauriModule:"Event",message:{cmd:"listen",event:e,windowLabel:t,handler:s(n)}}).then(i=>async()=>f(e,i))}async function U(e,t,n){return d(e,t,i=>{n(i),f(e,i.id).catch(()=>{})})}async function E(e,t){return d(e,null,t)}async function y(e,t){return U(e,null,t)}async function c(e,t){return _(e,void 0,t)}async function p(e){return E("tauri://update-status",t=>{e(t?.payload)})}async function I(){let e;function t(){e&&e(),e=void 0}return new Promise((n,i)=>{function o(a){if(a.error){t(),i(a.error);return}a.status==="DONE"&&(t(),n())}p(o).then(a=>{e=a}).catch(a=>{throw t(),a}),c("tauri://update-install").catch(a=>{throw t(),a})})}async function N(){let e;function t(){e&&e(),e=void 0}return new Promise((n,i)=>{function o(r){t(),n({manifest:r,shouldUpdate:!0})}function a(r){if(r.error){t(),i(r.error);return}r.status==="UPTODATE"&&(t(),n({shouldUpdate:!1}))}y("tauri://update-available",r=>{o(r?.payload)}).catch(r=>{throw t(),r}),p(a).then(r=>{e=r}).catch(r=>{throw t(),r}),c("tauri://update").catch(r=>{throw t(),r})})}
