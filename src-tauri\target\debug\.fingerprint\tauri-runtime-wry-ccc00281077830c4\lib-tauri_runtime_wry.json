{"rustc": 1842507548689473721, "features": "[\"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 1497995260721082616, "deps": [[4381063397040571828, "webview2_com", false, 7020866980887193561], [7653476968652377684, "windows", false, 3713878180079890005], [8292277814562636972, "tauri_utils", false, 12257840116341748066], [8319709847752024821, "uuid", false, 15208946729050052671], [8391357152270261188, "wry", false, 7675888078914974024], [11693073011723388840, "raw_window_handle", false, 15322742465324411442], [13208667028893622512, "rand", false, 2943278044707951345], [14162324460024849578, "tauri_runtime", false, 10021992693577726700], [16228250612241359704, "build_script_build", false, 3952907610622358473]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-ccc00281077830c4\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}