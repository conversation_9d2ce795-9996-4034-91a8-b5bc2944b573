"use strict";var a=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var p=Object.prototype.hasOwnProperty;var _=(e,r)=>{for(var t in r)a(e,t,{get:r[t],enumerable:!0})},g=(e,r,t,o)=>{if(r&&typeof r=="object"||typeof r=="function")for(let n of m(r))!p.call(e,n)&&n!==t&&a(e,n,{get:()=>r[n],enumerable:!(o=d(r,n))||o.enumerable});return e};var w=e=>g(a({},"__esModule",{value:!0}),e);var v={};_(v,{readText:()=>y,writeText:()=>T});module.exports=w(v);function f(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function l(e,r=!1){let t=f(),o=`_${t}`;return Object.defineProperty(window,o,{value:n=>(r&&Reflect.deleteProperty(window,o),e?.(n)),writable:!1,configurable:!0}),t}async function c(e,r={}){return new Promise((t,o)=>{let n=l(i=>{t(i),Reflect.deleteProperty(window,`_${u}`)},!0),u=l(i=>{o(i),Reflect.deleteProperty(window,`_${n}`)},!0);window.__TAURI_IPC__({cmd:e,callback:n,error:u,...r})})}async function s(e){return c("tauri",e)}async function T(e){return s({__tauriModule:"Clipboard",message:{cmd:"writeText",data:e}})}async function y(){return s({__tauriModule:"Clipboard",message:{cmd:"readText",data:null}})}
