"use strict";var a=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var g=Object.prototype.hasOwnProperty;var p=(e,r)=>{for(var t in r)a(e,t,{get:r[t],enumerable:!0})},_=(e,r,t,o)=>{if(r&&typeof r=="object"||typeof r=="function")for(let n of m(r))!g.call(e,n)&&n!==t&&a(e,n,{get:()=>r[n],enumerable:!(o=d(r,n))||o.enumerable});return e};var f=e=>_(a({},"__esModule",{value:!0}),e);var y={};p(y,{getMatches:()=>M});module.exports=f(y);function w(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function s(e,r=!1){let t=w(),o=`_${t}`;return Object.defineProperty(window,o,{value:n=>(r&&Reflect.deleteProperty(window,o),e?.(n)),writable:!1,configurable:!0}),t}async function u(e,r={}){return new Promise((t,o)=>{let n=s(i=>{t(i),Reflect.deleteProperty(window,`_${c}`)},!0),c=s(i=>{o(i),Reflect.deleteProperty(window,`_${n}`)},!0);window.__TAURI_IPC__({cmd:e,callback:n,error:c,...r})})}async function l(e){return u("tauri",e)}async function M(){return l({__tauriModule:"Cli",message:{cmd:"cliMatches"}})}
