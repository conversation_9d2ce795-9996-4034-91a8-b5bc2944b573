{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 16335202704479430798, "path": 3810753131390094618, "deps": [[4450062412064442726, "dirs_next", false, 11730094918614587851], [4899080583175475170, "semver", false, 5026173323402683378], [7468248713591957673, "cargo_toml", false, 7009215734380443902], [8292277814562636972, "tauri_utils", false, 14921682461595741699], [8569119365930580996, "serde_json", false, 12964243539307167415], [9689903380558560274, "serde", false, 16535090710719947447], [10301936376833819828, "json_patch", false, 14730323624259915036], [13077543566650298139, "heck", false, 17114546617626299270], [13625485746686963219, "anyhow", false, 10277305641412972355], [14189313126492979171, "tauri_winres", false, 9460671078915690802], [15622660310229662834, "walkdir", false, 12264954739632947696]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-54d5835a8250f078\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}