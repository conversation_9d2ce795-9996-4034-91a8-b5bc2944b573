"use strict";var a=Object.defineProperty;var l=Object.getOwnPropertyDescriptor;var p=Object.getOwnPropertyNames;var m=Object.prototype.hasOwnProperty;var g=(e,r)=>{for(var o in r)a(e,o,{get:r[o],enumerable:!0})},_=(e,r,o,t)=>{if(r&&typeof r=="object"||typeof r=="function")for(let n of p(r))!m.call(e,n)&&n!==o&&a(e,n,{get:()=>r[n],enumerable:!(t=l(r,n))||t.enumerable});return e};var f=e=>_(a({},"__esModule",{value:!0}),e);var k={};g(k,{getName:()=>T,getTauriVersion:()=>v,getVersion:()=>y,hide:()=>P,show:()=>A});module.exports=f(k);function w(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function c(e,r=!1){let o=w(),t=`_${o}`;return Object.defineProperty(window,t,{value:n=>(r&&Reflect.deleteProperty(window,t),e?.(n)),writable:!1,configurable:!0}),o}async function d(e,r={}){return new Promise((o,t)=>{let n=c(s=>{o(s),Reflect.deleteProperty(window,`_${u}`)},!0),u=c(s=>{t(s),Reflect.deleteProperty(window,`_${n}`)},!0);window.__TAURI_IPC__({cmd:e,callback:n,error:u,...r})})}async function i(e){return d("tauri",e)}async function y(){return i({__tauriModule:"App",message:{cmd:"getAppVersion"}})}async function T(){return i({__tauriModule:"App",message:{cmd:"getAppName"}})}async function v(){return i({__tauriModule:"App",message:{cmd:"getTauriVersion"}})}async function A(){return i({__tauriModule:"App",message:{cmd:"show"}})}async function P(){return i({__tauriModule:"App",message:{cmd:"hide"}})}
