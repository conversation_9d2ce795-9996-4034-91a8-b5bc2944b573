{"rustc": 1842507548689473721, "features": "[\"file-drop\", \"objc-exception\", \"protocol\"]", "declared_features": "[\"default\", \"devtools\", \"dox\", \"file-drop\", \"fullscreen\", \"linux-headers\", \"objc-exception\", \"protocol\", \"tracing\", \"transparent\", \"tray\"]", "target": 2463569863749872413, "profile": 2241668132362809309, "path": 9546548099588661534, "deps": [[3007252114546291461, "tao", false, 14505684108018153607], [3150220818285335163, "url", false, 8956527005242435352], [3540822385484940109, "windows_implement", false, 4408266691572968263], [3722963349756955755, "once_cell", false, 10281967339976405426], [4381063397040571828, "webview2_com", false, 7020866980887193561], [4405182208873388884, "http", false, 3917179729382657189], [4684437522915235464, "libc", false, 11330886391774495882], [5986029879202738730, "log", false, 7349739194943966833], [7653476968652377684, "windows", false, 3713878180079890005], [8008191657135824715, "thiserror", false, 2993888177908691868], [8391357152270261188, "build_script_build", false, 5553815382231770682], [8569119365930580996, "serde_json", false, 14354482580527557019], [9689903380558560274, "serde", false, 2554312950418482273], [11989259058781683633, "dunce", false, 14967221997103913491]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-aa9e3351efe791cf\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}