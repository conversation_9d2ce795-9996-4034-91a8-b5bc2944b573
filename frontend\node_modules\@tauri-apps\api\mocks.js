import"./chunk-FEIY7W7S.js";function w(n){window.__TAURI_IPC__=async({cmd:o,callback:_,error:e,...i})=>{try{window[`_${_}`](await n(o,i))}catch(r){window[`_${e}`](r)}}}function t(n,...o){window.__TAURI_METADATA__={__windows:[n,...o].map(_=>({label:_})),__currentWindow:{label:n}}}function d(){delete window.__TAURI_IPC__,delete window.__TAURI_METADATA__}export{d as clearMocks,w as mockIPC,t as mockWindows};
