"use strict";var L=Object.defineProperty;var pe=Object.getOwnPropertyDescriptor;var ge=Object.getOwnPropertyNames;var ye=Object.prototype.hasOwnProperty;var d=(t,e)=>{for(var i in e)L(t,i,{get:e[i],enumerable:!0})},he=(t,e,i,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of ge(e))!ye.call(t,s)&&s!==i&&L(t,s,{get:()=>e[s],enumerable:!(r=pe(e,s))||r.enumerable});return t};var fe=t=>he(L({},"__esModule",{value:!0}),t);var Zt={};d(Zt,{app:()=>k,cli:()=>U,clipboard:()=>I,dialog:()=>z,event:()=>V,fs:()=>j,globalShortcut:()=>q,http:()=>J,invoke:()=>Yt,notification:()=>$,os:()=>ne,path:()=>K,process:()=>Q,shell:()=>Y,tauri:()=>R,updater:()=>X,window:()=>ie});module.exports=fe(Zt);var k={};d(k,{getName:()=>we,getTauriVersion:()=>ve,getVersion:()=>Pe,hide:()=>Te,show:()=>Me});var R={};d(R,{convertFileSrc:()=>_e,invoke:()=>f,transformCallback:()=>c});function be(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function c(t,e=!1){let i=be(),r=`_${i}`;return Object.defineProperty(window,r,{value:s=>(e&&Reflect.deleteProperty(window,r),t?.(s)),writable:!1,configurable:!0}),i}async function f(t,e={}){return new Promise((i,r)=>{let s=c(l=>{i(l),Reflect.deleteProperty(window,`_${o}`)},!0),o=c(l=>{r(l),Reflect.deleteProperty(window,`_${s}`)},!0);window.__TAURI_IPC__({cmd:t,callback:s,error:o,...e})})}function _e(t,e="asset"){return window.__TAURI__.convertFileSrc(t,e)}async function n(t){return f("tauri",t)}async function Pe(){return n({__tauriModule:"App",message:{cmd:"getAppVersion"}})}async function we(){return n({__tauriModule:"App",message:{cmd:"getAppName"}})}async function ve(){return n({__tauriModule:"App",message:{cmd:"getTauriVersion"}})}async function Me(){return n({__tauriModule:"App",message:{cmd:"show"}})}async function Te(){return n({__tauriModule:"App",message:{cmd:"hide"}})}var U={};d(U,{getMatches:()=>Fe});async function Fe(){return n({__tauriModule:"Cli",message:{cmd:"cliMatches"}})}var I={};d(I,{readText:()=>Ee,writeText:()=>Oe});async function Oe(t){return n({__tauriModule:"Clipboard",message:{cmd:"writeText",data:t}})}async function Ee(){return n({__tauriModule:"Clipboard",message:{cmd:"readText",data:null}})}var z={};d(z,{ask:()=>De,confirm:()=>Se,message:()=>We,open:()=>Ce,save:()=>Ae});async function Ce(t={}){return typeof t=="object"&&Object.freeze(t),n({__tauriModule:"Dialog",message:{cmd:"openDialog",options:t}})}async function Ae(t={}){return typeof t=="object"&&Object.freeze(t),n({__tauriModule:"Dialog",message:{cmd:"saveDialog",options:t}})}async function We(t,e){let i=typeof e=="string"?{title:e}:e;return n({__tauriModule:"Dialog",message:{cmd:"messageDialog",message:t.toString(),title:i?.title?.toString(),type:i?.type,buttonLabel:i?.okLabel?.toString()}})}async function De(t,e){let i=typeof e=="string"?{title:e}:e;return n({__tauriModule:"Dialog",message:{cmd:"askDialog",message:t.toString(),title:i?.title?.toString(),type:i?.type,buttonLabels:[i?.okLabel?.toString()??"Yes",i?.cancelLabel?.toString()??"No"]}})}async function Se(t,e){let i=typeof e=="string"?{title:e}:e;return n({__tauriModule:"Dialog",message:{cmd:"confirmDialog",message:t.toString(),title:i?.title?.toString(),type:i?.type,buttonLabels:[i?.okLabel?.toString()??"Ok",i?.cancelLabel?.toString()??"Cancel"]}})}var V={};d(V,{TauriEvent:()=>M,emit:()=>T,listen:()=>N,once:()=>H});async function re(t,e){return n({__tauriModule:"Event",message:{cmd:"unlisten",event:t,eventId:e}})}async function w(t,e,i){await n({__tauriModule:"Event",message:{cmd:"emit",event:t,windowLabel:e,payload:i}})}async function b(t,e,i){return n({__tauriModule:"Event",message:{cmd:"listen",event:t,windowLabel:e,handler:c(i)}}).then(r=>async()=>re(t,r))}async function v(t,e,i){return b(t,e,r=>{i(r),re(t,r.id).catch(()=>{})})}var M=(u=>(u.WINDOW_RESIZED="tauri://resize",u.WINDOW_MOVED="tauri://move",u.WINDOW_CLOSE_REQUESTED="tauri://close-requested",u.WINDOW_CREATED="tauri://window-created",u.WINDOW_DESTROYED="tauri://destroyed",u.WINDOW_FOCUS="tauri://focus",u.WINDOW_BLUR="tauri://blur",u.WINDOW_SCALE_FACTOR_CHANGED="tauri://scale-change",u.WINDOW_THEME_CHANGED="tauri://theme-changed",u.WINDOW_FILE_DROP="tauri://file-drop",u.WINDOW_FILE_DROP_HOVER="tauri://file-drop-hover",u.WINDOW_FILE_DROP_CANCELLED="tauri://file-drop-cancelled",u.MENU="tauri://menu",u.CHECK_UPDATE="tauri://update",u.UPDATE_AVAILABLE="tauri://update-available",u.INSTALL_UPDATE="tauri://update-install",u.STATUS_UPDATE="tauri://update-status",u.DOWNLOAD_PROGRESS="tauri://update-download-progress",u))(M||{});async function N(t,e){return b(t,null,e)}async function H(t,e){return v(t,null,e)}async function T(t,e){return w(t,void 0,e)}var j={};d(j,{BaseDirectory:()=>F,Dir:()=>F,copyFile:()=>He,createDir:()=>ze,exists:()=>qe,readBinaryFile:()=>Re,readDir:()=>Ie,readTextFile:()=>Le,removeDir:()=>Ne,removeFile:()=>Ve,renameFile:()=>je,writeBinaryFile:()=>Ue,writeFile:()=>ke,writeTextFile:()=>ke});var F=(a=>(a[a.Audio=1]="Audio",a[a.Cache=2]="Cache",a[a.Config=3]="Config",a[a.Data=4]="Data",a[a.LocalData=5]="LocalData",a[a.Desktop=6]="Desktop",a[a.Document=7]="Document",a[a.Download=8]="Download",a[a.Executable=9]="Executable",a[a.Font=10]="Font",a[a.Home=11]="Home",a[a.Picture=12]="Picture",a[a.Public=13]="Public",a[a.Runtime=14]="Runtime",a[a.Template=15]="Template",a[a.Video=16]="Video",a[a.Resource=17]="Resource",a[a.App=18]="App",a[a.Log=19]="Log",a[a.Temp=20]="Temp",a[a.AppConfig=21]="AppConfig",a[a.AppData=22]="AppData",a[a.AppLocalData=23]="AppLocalData",a[a.AppCache=24]="AppCache",a[a.AppLog=25]="AppLog",a))(F||{});async function Le(t,e={}){return n({__tauriModule:"Fs",message:{cmd:"readTextFile",path:t,options:e}})}async function Re(t,e={}){let i=await n({__tauriModule:"Fs",message:{cmd:"readFile",path:t,options:e}});return Uint8Array.from(i)}async function ke(t,e,i){typeof i=="object"&&Object.freeze(i),typeof t=="object"&&Object.freeze(t);let r={path:"",contents:""},s=i;return typeof t=="string"?r.path=t:(r.path=t.path,r.contents=t.contents),typeof e=="string"?r.contents=e??"":s=e,n({__tauriModule:"Fs",message:{cmd:"writeFile",path:r.path,contents:Array.from(new TextEncoder().encode(r.contents)),options:s}})}async function Ue(t,e,i){typeof i=="object"&&Object.freeze(i),typeof t=="object"&&Object.freeze(t);let r={path:"",contents:[]},s=i;return typeof t=="string"?r.path=t:(r.path=t.path,r.contents=t.contents),e&&"dir"in e?s=e:typeof t=="string"&&(r.contents=e??[]),n({__tauriModule:"Fs",message:{cmd:"writeFile",path:r.path,contents:Array.from(r.contents instanceof ArrayBuffer?new Uint8Array(r.contents):r.contents),options:s}})}async function Ie(t,e={}){return n({__tauriModule:"Fs",message:{cmd:"readDir",path:t,options:e}})}async function ze(t,e={}){return n({__tauriModule:"Fs",message:{cmd:"createDir",path:t,options:e}})}async function Ne(t,e={}){return n({__tauriModule:"Fs",message:{cmd:"removeDir",path:t,options:e}})}async function He(t,e,i={}){return n({__tauriModule:"Fs",message:{cmd:"copyFile",source:t,destination:e,options:i}})}async function Ve(t,e={}){return n({__tauriModule:"Fs",message:{cmd:"removeFile",path:t,options:e}})}async function je(t,e,i={}){return n({__tauriModule:"Fs",message:{cmd:"renameFile",oldPath:t,newPath:e,options:i}})}async function qe(t,e={}){return n({__tauriModule:"Fs",message:{cmd:"exists",path:t,options:e}})}var q={};d(q,{isRegistered:()=>$e,register:()=>Ge,registerAll:()=>Je,unregister:()=>Ke,unregisterAll:()=>Qe});async function Ge(t,e){return n({__tauriModule:"GlobalShortcut",message:{cmd:"register",shortcut:t,handler:c(e)}})}async function Je(t,e){return n({__tauriModule:"GlobalShortcut",message:{cmd:"registerAll",shortcuts:t,handler:c(e)}})}async function $e(t){return n({__tauriModule:"GlobalShortcut",message:{cmd:"isRegistered",shortcut:t}})}async function Ke(t){return n({__tauriModule:"GlobalShortcut",message:{cmd:"unregister",shortcut:t}})}async function Qe(){return n({__tauriModule:"GlobalShortcut",message:{cmd:"unregisterAll"}})}var J={};d(J,{Body:()=>p,Client:()=>E,Response:()=>O,ResponseType:()=>se,fetch:()=>Ze,getClient:()=>ae});var se=(r=>(r[r.JSON=1]="JSON",r[r.Text=2]="Text",r[r.Binary=3]="Binary",r))(se||{});async function Ye(t){let e={},i=async(r,s)=>{if(s!==null){let o;typeof s=="string"?o=s:s instanceof Uint8Array||Array.isArray(s)?o=Array.from(s):s instanceof File?o={file:Array.from(new Uint8Array(await s.arrayBuffer())),mime:s.type,fileName:s.name}:typeof s.file=="string"?o={file:s.file,mime:s.mime,fileName:s.fileName}:o={file:Array.from(s.file),mime:s.mime,fileName:s.fileName},e[String(r)]=o}};if(t instanceof FormData)for(let[r,s]of t)await i(r,s);else for(let[r,s]of Object.entries(t))await i(r,s);return e}var p=class{constructor(e,i){this.type=e,this.payload=i}static form(e){return new p("Form",e)}static json(e){return new p("Json",e)}static text(e){return new p("Text",e)}static bytes(e){return new p("Bytes",Array.from(e instanceof ArrayBuffer?new Uint8Array(e):e))}},O=class{constructor(e){this.url=e.url,this.status=e.status,this.ok=this.status>=200&&this.status<300,this.headers=e.headers,this.rawHeaders=e.rawHeaders,this.data=e.data}},E=class{constructor(e){this.id=e}async drop(){return n({__tauriModule:"Http",message:{cmd:"dropClient",client:this.id}})}async request(e){let i=!e.responseType||e.responseType===1;return i&&(e.responseType=2),e.body?.type==="Form"&&(e.body.payload=await Ye(e.body.payload)),n({__tauriModule:"Http",message:{cmd:"httpRequest",client:this.id,options:e}}).then(r=>{let s=new O(r);if(i){try{s.data=JSON.parse(s.data)}catch(o){if(s.ok&&s.data==="")s.data={};else if(s.ok)throw Error(`Failed to parse response \`${s.data}\` as JSON: ${o};
              try setting the \`responseType\` option to \`ResponseType.Text\` or \`ResponseType.Binary\` if the API does not return a JSON response.`)}return s}return s})}async get(e,i){return this.request({method:"GET",url:e,...i})}async post(e,i,r){return this.request({method:"POST",url:e,body:i,...r})}async put(e,i,r){return this.request({method:"PUT",url:e,body:i,...r})}async patch(e,i){return this.request({method:"PATCH",url:e,...i})}async delete(e,i){return this.request({method:"DELETE",url:e,...i})}};async function ae(t){return n({__tauriModule:"Http",message:{cmd:"createClient",options:t}}).then(e=>new E(e))}var G=null;async function Ze(t,e){return G===null&&(G=await ae()),G.request({url:t,method:e?.method??"GET",...e})}var $={};d($,{isPermissionGranted:()=>Xe,requestPermission:()=>Be,sendNotification:()=>et});async function Xe(){return window.Notification.permission!=="default"?Promise.resolve(window.Notification.permission==="granted"):n({__tauriModule:"Notification",message:{cmd:"isNotificationPermissionGranted"}})}async function Be(){return window.Notification.requestPermission()}function et(t){typeof t=="string"?new window.Notification(t):new window.Notification(t.title,t)}var K={};d(K,{BaseDirectory:()=>F,appCacheDir:()=>rt,appConfigDir:()=>oe,appDataDir:()=>it,appDir:()=>tt,appLocalDataDir:()=>nt,appLogDir:()=>le,audioDir:()=>st,basename:()=>Dt,cacheDir:()=>at,configDir:()=>ot,dataDir:()=>lt,delimiter:()=>Ft,desktopDir:()=>ut,dirname:()=>At,documentDir:()=>dt,downloadDir:()=>ct,executableDir:()=>mt,extname:()=>Wt,fontDir:()=>pt,homeDir:()=>gt,isAbsolute:()=>St,join:()=>Ct,localDataDir:()=>yt,logDir:()=>Mt,normalize:()=>Et,pictureDir:()=>ht,publicDir:()=>ft,resolve:()=>Ot,resolveResource:()=>_t,resourceDir:()=>bt,runtimeDir:()=>Pt,sep:()=>Tt,templateDir:()=>wt,videoDir:()=>vt});function _(){return navigator.appVersion.includes("Win")}async function tt(){return oe()}async function oe(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:21}})}async function it(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:22}})}async function nt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:23}})}async function rt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:24}})}async function st(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:1}})}async function at(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:2}})}async function ot(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:3}})}async function lt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:4}})}async function ut(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:6}})}async function dt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:7}})}async function ct(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:8}})}async function mt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:9}})}async function pt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:10}})}async function gt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:11}})}async function yt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:5}})}async function ht(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:12}})}async function ft(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:13}})}async function bt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:17}})}async function _t(t){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:t,directory:17}})}async function Pt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:14}})}async function wt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:15}})}async function vt(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:16}})}async function Mt(){return le()}async function le(){return n({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:25}})}var Tt=_()?"\\":"/",Ft=_()?";":":";async function Ot(...t){return n({__tauriModule:"Path",message:{cmd:"resolve",paths:t}})}async function Et(t){return n({__tauriModule:"Path",message:{cmd:"normalize",path:t}})}async function Ct(...t){return n({__tauriModule:"Path",message:{cmd:"join",paths:t}})}async function At(t){return n({__tauriModule:"Path",message:{cmd:"dirname",path:t}})}async function Wt(t){return n({__tauriModule:"Path",message:{cmd:"extname",path:t}})}async function Dt(t,e){return n({__tauriModule:"Path",message:{cmd:"basename",path:t,ext:e}})}async function St(t){return n({__tauriModule:"Path",message:{cmd:"isAbsolute",path:t}})}var Q={};d(Q,{exit:()=>xt,relaunch:()=>Lt});async function xt(t=0){return n({__tauriModule:"Process",message:{cmd:"exit",exitCode:t}})}async function Lt(){return n({__tauriModule:"Process",message:{cmd:"relaunch"}})}var Y={};d(Y,{Child:()=>C,Command:()=>P,EventEmitter:()=>g,open:()=>kt});async function Rt(t,e,i=[],r){return typeof i=="object"&&Object.freeze(i),n({__tauriModule:"Shell",message:{cmd:"execute",program:e,args:i,options:r,onEventFn:c(t)}})}var g=class{constructor(){this.eventListeners=Object.create(null)}addListener(e,i){return this.on(e,i)}removeListener(e,i){return this.off(e,i)}on(e,i){return e in this.eventListeners?this.eventListeners[e].push(i):this.eventListeners[e]=[i],this}once(e,i){let r=(...s)=>{this.removeListener(e,r),i(...s)};return this.addListener(e,r)}off(e,i){return e in this.eventListeners&&(this.eventListeners[e]=this.eventListeners[e].filter(r=>r!==i)),this}removeAllListeners(e){return e?delete this.eventListeners[e]:this.eventListeners=Object.create(null),this}emit(e,...i){if(e in this.eventListeners){let r=this.eventListeners[e];for(let s of r)s(...i);return!0}return!1}listenerCount(e){return e in this.eventListeners?this.eventListeners[e].length:0}prependListener(e,i){return e in this.eventListeners?this.eventListeners[e].unshift(i):this.eventListeners[e]=[i],this}prependOnceListener(e,i){let r=(...s)=>{this.removeListener(e,r),i(...s)};return this.prependListener(e,r)}},C=class{constructor(e){this.pid=e}async write(e){return n({__tauriModule:"Shell",message:{cmd:"stdinWrite",pid:this.pid,buffer:typeof e=="string"?e:Array.from(e)}})}async kill(){return n({__tauriModule:"Shell",message:{cmd:"killChild",pid:this.pid}})}},P=class extends g{constructor(i,r=[],s){super();this.stdout=new g;this.stderr=new g;this.program=i,this.args=typeof r=="string"?[r]:r,this.options=s??{}}static sidecar(i,r=[],s){let o=new P(i,r,s);return o.options.sidecar=!0,o}async spawn(){return Rt(i=>{switch(i.event){case"Error":this.emit("error",i.payload);break;case"Terminated":this.emit("close",i.payload);break;case"Stdout":this.stdout.emit("data",i.payload);break;case"Stderr":this.stderr.emit("data",i.payload);break}},this.program,this.args,this.options).then(i=>new C(i))}async execute(){return new Promise((i,r)=>{this.on("error",r);let s=[],o=[];this.stdout.on("data",l=>{s.push(l)}),this.stderr.on("data",l=>{o.push(l)}),this.on("close",l=>{i({code:l.code,signal:l.signal,stdout:s.join(`
`),stderr:o.join(`
`)})}),this.spawn().catch(r)})}};async function kt(t,e){return n({__tauriModule:"Shell",message:{cmd:"open",path:t,with:e}})}var X={};d(X,{checkUpdate:()=>It,installUpdate:()=>Ut,onUpdaterEvent:()=>Z});async function Z(t){return N("tauri://update-status",e=>{t(e?.payload)})}async function Ut(){let t;function e(){t&&t(),t=void 0}return new Promise((i,r)=>{function s(o){if(o.error){e(),r(o.error);return}o.status==="DONE"&&(e(),i())}Z(s).then(o=>{t=o}).catch(o=>{throw e(),o}),T("tauri://update-install").catch(o=>{throw e(),o})})}async function It(){let t;function e(){t&&t(),t=void 0}return new Promise((i,r)=>{function s(l){e(),i({manifest:l,shouldUpdate:!0})}function o(l){if(l.error){e(),r(l.error);return}l.status==="UPTODATE"&&(e(),i({shouldUpdate:!1}))}H("tauri://update-available",l=>{s(l?.payload)}).catch(l=>{throw e(),l}),Z(o).then(l=>{t=l}).catch(l=>{throw e(),l}),T("tauri://update").catch(l=>{throw e(),l})})}var ie={};d(ie,{CloseRequestedEvent:()=>x,LogicalPosition:()=>W,LogicalSize:()=>A,PhysicalPosition:()=>h,PhysicalSize:()=>y,UserAttentionType:()=>de,WebviewWindow:()=>m,WebviewWindowHandle:()=>D,WindowManager:()=>S,appWindow:()=>ee,availableMonitors:()=>Vt,currentMonitor:()=>Nt,getAll:()=>B,getCurrent:()=>zt,primaryMonitor:()=>Ht});var A=class{constructor(e,i){this.type="Logical";this.width=e,this.height=i}},y=class{constructor(e,i){this.type="Physical";this.width=e,this.height=i}toLogical(e){return new A(this.width/e,this.height/e)}},W=class{constructor(e,i){this.type="Logical";this.x=e,this.y=i}},h=class{constructor(e,i){this.type="Physical";this.x=e,this.y=i}toLogical(e){return new W(this.x/e,this.y/e)}},de=(i=>(i[i.Critical=1]="Critical",i[i.Informational=2]="Informational",i))(de||{});function zt(){return new m(window.__TAURI_METADATA__.__currentWindow.label,{skip:!0})}function B(){return window.__TAURI_METADATA__.__windows.map(t=>new m(t.label,{skip:!0}))}var ue=["tauri://created","tauri://error"],D=class{constructor(e){this.label=e,this.listeners=Object.create(null)}async listen(e,i){return this._handleTauriEvent(e,i)?Promise.resolve(()=>{let r=this.listeners[e];r.splice(r.indexOf(i),1)}):b(e,this.label,i)}async once(e,i){return this._handleTauriEvent(e,i)?Promise.resolve(()=>{let r=this.listeners[e];r.splice(r.indexOf(i),1)}):v(e,this.label,i)}async emit(e,i){if(ue.includes(e)){for(let r of this.listeners[e]||[])r({event:e,id:-1,windowLabel:this.label,payload:i});return Promise.resolve()}return w(e,this.label,i)}_handleTauriEvent(e,i){return ue.includes(e)?(e in this.listeners?this.listeners[e].push(i):this.listeners[e]=[i],!0):!1}},S=class extends D{async scaleFactor(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"scaleFactor"}}}})}async innerPosition(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"innerPosition"}}}}).then(({x:e,y:i})=>new h(e,i))}async outerPosition(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"outerPosition"}}}}).then(({x:e,y:i})=>new h(e,i))}async innerSize(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"innerSize"}}}}).then(({width:e,height:i})=>new y(e,i))}async outerSize(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"outerSize"}}}}).then(({width:e,height:i})=>new y(e,i))}async isFullscreen(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"isFullscreen"}}}})}async isMinimized(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"isMinimized"}}}})}async isMaximized(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"isMaximized"}}}})}async isFocused(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"isFocused"}}}})}async isDecorated(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"isDecorated"}}}})}async isResizable(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"isResizable"}}}})}async isMaximizable(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"isMaximizable"}}}})}async isMinimizable(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"isMinimizable"}}}})}async isClosable(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"isClosable"}}}})}async isVisible(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"isVisible"}}}})}async title(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"title"}}}})}async theme(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"theme"}}}})}async center(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"center"}}}})}async requestUserAttention(e){let i=null;return e&&(e===1?i={type:"Critical"}:i={type:"Informational"}),n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"requestUserAttention",payload:i}}}})}async setResizable(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setResizable",payload:e}}}})}async setMaximizable(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setMaximizable",payload:e}}}})}async setMinimizable(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setMinimizable",payload:e}}}})}async setClosable(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setClosable",payload:e}}}})}async setTitle(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setTitle",payload:e}}}})}async maximize(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"maximize"}}}})}async unmaximize(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"unmaximize"}}}})}async toggleMaximize(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"toggleMaximize"}}}})}async minimize(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"minimize"}}}})}async unminimize(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"unminimize"}}}})}async show(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"show"}}}})}async hide(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"hide"}}}})}async close(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"close"}}}})}async setDecorations(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setDecorations",payload:e}}}})}async setAlwaysOnTop(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setAlwaysOnTop",payload:e}}}})}async setContentProtected(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setContentProtected",payload:e}}}})}async setSize(e){if(!e||e.type!=="Logical"&&e.type!=="Physical")throw new Error("the `size` argument must be either a LogicalSize or a PhysicalSize instance");return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setSize",payload:{type:e.type,data:{width:e.width,height:e.height}}}}}})}async setMinSize(e){if(e&&e.type!=="Logical"&&e.type!=="Physical")throw new Error("the `size` argument must be either a LogicalSize or a PhysicalSize instance");return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setMinSize",payload:e?{type:e.type,data:{width:e.width,height:e.height}}:null}}}})}async setMaxSize(e){if(e&&e.type!=="Logical"&&e.type!=="Physical")throw new Error("the `size` argument must be either a LogicalSize or a PhysicalSize instance");return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setMaxSize",payload:e?{type:e.type,data:{width:e.width,height:e.height}}:null}}}})}async setPosition(e){if(!e||e.type!=="Logical"&&e.type!=="Physical")throw new Error("the `position` argument must be either a LogicalPosition or a PhysicalPosition instance");return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setPosition",payload:{type:e.type,data:{x:e.x,y:e.y}}}}}})}async setFullscreen(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setFullscreen",payload:e}}}})}async setFocus(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setFocus"}}}})}async setIcon(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setIcon",payload:{icon:typeof e=="string"?e:Array.from(e)}}}}})}async setSkipTaskbar(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setSkipTaskbar",payload:e}}}})}async setCursorGrab(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setCursorGrab",payload:e}}}})}async setCursorVisible(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setCursorVisible",payload:e}}}})}async setCursorIcon(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setCursorIcon",payload:e}}}})}async setCursorPosition(e){if(!e||e.type!=="Logical"&&e.type!=="Physical")throw new Error("the `position` argument must be either a LogicalPosition or a PhysicalPosition instance");return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setCursorPosition",payload:{type:e.type,data:{x:e.x,y:e.y}}}}}})}async setIgnoreCursorEvents(e){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"setIgnoreCursorEvents",payload:e}}}})}async startDragging(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{label:this.label,cmd:{type:"startDragging"}}}})}async onResized(e){return this.listen("tauri://resize",i=>{i.payload=me(i.payload),e(i)})}async onMoved(e){return this.listen("tauri://move",i=>{i.payload=ce(i.payload),e(i)})}async onCloseRequested(e){return this.listen("tauri://close-requested",i=>{let r=new x(i);Promise.resolve(e(r)).then(()=>{if(!r.isPreventDefault())return this.close()})})}async onFocusChanged(e){let i=await this.listen("tauri://focus",s=>{e({...s,payload:!0})}),r=await this.listen("tauri://blur",s=>{e({...s,payload:!1})});return()=>{i(),r()}}async onScaleChanged(e){return this.listen("tauri://scale-change",e)}async onMenuClicked(e){return this.listen("tauri://menu",e)}async onFileDropEvent(e){let i=await this.listen("tauri://file-drop",o=>{e({...o,payload:{type:"drop",paths:o.payload}})}),r=await this.listen("tauri://file-drop-hover",o=>{e({...o,payload:{type:"hover",paths:o.payload}})}),s=await this.listen("tauri://file-drop-cancelled",o=>{e({...o,payload:{type:"cancel"}})});return()=>{i(),r(),s()}}async onThemeChanged(e){return this.listen("tauri://theme-changed",e)}},x=class{constructor(e){this._preventDefault=!1;this.event=e.event,this.windowLabel=e.windowLabel,this.id=e.id}preventDefault(){this._preventDefault=!0}isPreventDefault(){return this._preventDefault}},m=class extends S{constructor(e,i={}){super(e),i?.skip||n({__tauriModule:"Window",message:{cmd:"createWebview",data:{options:{label:e,...i}}}}).then(async()=>this.emit("tauri://created")).catch(async r=>this.emit("tauri://error",r))}static getByLabel(e){return B().some(i=>i.label===e)?new m(e,{skip:!0}):null}static async getFocusedWindow(){for(let e of B())if(await e.isFocused())return e;return null}},ee;"__TAURI_METADATA__"in window?ee=new m(window.__TAURI_METADATA__.__currentWindow.label,{skip:!0}):(console.warn(`Could not find "window.__TAURI_METADATA__". The "appWindow" value will reference the "main" window label.
Note that this is not an issue if running this frontend on a browser instead of a Tauri window.`),ee=new m("main",{skip:!0}));function te(t){return t===null?null:{name:t.name,scaleFactor:t.scaleFactor,position:ce(t.position),size:me(t.size)}}function ce(t){return new h(t.x,t.y)}function me(t){return new y(t.width,t.height)}async function Nt(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{cmd:{type:"currentMonitor"}}}}).then(te)}async function Ht(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{cmd:{type:"primaryMonitor"}}}}).then(te)}async function Vt(){return n({__tauriModule:"Window",message:{cmd:"manage",data:{cmd:{type:"availableMonitors"}}}}).then(t=>t.map(te))}var ne={};d(ne,{EOL:()=>jt,arch:()=>$t,locale:()=>Qt,platform:()=>qt,tempdir:()=>Kt,type:()=>Jt,version:()=>Gt});var jt=_()?`\r
`:`
`;async function qt(){return n({__tauriModule:"Os",message:{cmd:"platform"}})}async function Gt(){return n({__tauriModule:"Os",message:{cmd:"version"}})}async function Jt(){return n({__tauriModule:"Os",message:{cmd:"osType"}})}async function $t(){return n({__tauriModule:"Os",message:{cmd:"arch"}})}async function Kt(){return n({__tauriModule:"Os",message:{cmd:"tempdir"}})}async function Qt(){return n({__tauriModule:"Os",message:{cmd:"locale"}})}var Yt=f;
