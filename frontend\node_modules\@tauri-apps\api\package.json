{"name": "@tauri-apps/api", "version": "1.5.0", "description": "Tauri API definitions", "type": "module", "funding": {"type": "opencollective", "url": "https://opencollective.com/tauri"}, "exports": {"./app": {"import": "./app.js", "require": "./app.cjs"}, "./cli": {"import": "./cli.js", "require": "./cli.cjs"}, "./clipboard": {"import": "./clipboard.js", "require": "./clipboard.cjs"}, "./dialog": {"import": "./dialog.js", "require": "./dialog.cjs"}, "./event": {"import": "./event.js", "require": "./event.cjs"}, "./fs": {"import": "./fs.js", "require": "./fs.cjs"}, "./globalShortcut": {"import": "./globalShortcut.js", "require": "./globalShortcut.cjs"}, "./http": {"import": "./http.js", "require": "./http.cjs"}, ".": {"import": "./index.js", "require": "./index.cjs"}, "./mocks": {"import": "./mocks.js", "require": "./mocks.cjs"}, "./notification": {"import": "./notification.js", "require": "./notification.cjs"}, "./os": {"import": "./os.js", "require": "./os.cjs"}, "./path": {"import": "./path.js", "require": "./path.cjs"}, "./process": {"import": "./process.js", "require": "./process.cjs"}, "./shell": {"import": "./shell.js", "require": "./shell.cjs"}, "./tauri": {"import": "./tauri.js", "require": "./tauri.cjs"}, "./updater": {"import": "./updater.js", "require": "./updater.cjs"}, "./window": {"import": "./window.js", "require": "./window.cjs"}, "./package.json": "./package.json"}, "scripts": {"build": "yarn tsup && node ./scripts/after-build.cjs", "npm-pack": "yarn build && cd ./dist && npm pack", "npm-publish": "yarn build && cd ./dist && yarn publish --access public --loglevel silly", "lint": "eslint --ext ts \"./src/**/*.ts\"", "lint-fix": "eslint --fix --ext ts \"./src/**/*.ts\"", "format": "prettier --write --end-of-line=auto \"./**/*.{cjs,js,jsx,ts,tsx,html,css,json}\" --ignore-path ../../.prettierignore", "format:check": "prettier --check --end-of-line=auto \"./**/*.{cjs,js,jsx,ts,tsx,html,css,json}\" --ignore-path ../../.prettierignore"}, "repository": {"type": "git", "url": "git+https://github.com/tauri-apps/tauri.git"}, "contributors": ["Tauri Programme within The Commons Conservancy"], "license": "Apache-2.0 OR MIT", "bugs": {"url": "https://github.com/tauri-apps/tauri/issues"}, "homepage": "https://github.com/tauri-apps/tauri#readme", "publishConfig": {"access": "public"}, "engines": {"node": ">= 14.6.0", "npm": ">= 6.6.0", "yarn": ">= 1.19.1"}, "devDependencies": {}, "resolutions": {"semver": ">=7.5.2", "optionator": ">=0.9.3"}}