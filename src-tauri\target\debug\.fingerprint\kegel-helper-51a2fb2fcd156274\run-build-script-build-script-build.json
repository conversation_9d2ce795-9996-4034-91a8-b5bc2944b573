{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[8207884286030937749, "build_script_build", false, 4721914303088259023]], "local": [{"RerunIfChanged": {"output": "debug\\build\\kegel-helper-51a2fb2fcd156274\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}