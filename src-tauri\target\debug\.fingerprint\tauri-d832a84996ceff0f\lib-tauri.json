{"rustc": 1842507548689473721, "features": "[\"compression\", \"custom-protocol\", \"default\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"objc-exception\", \"open\", \"path-all\", \"regex\", \"shell-open\", \"shell-open-api\", \"tauri-runtime-wry\", \"wry\"]", "declared_features": "[\"api-all\", \"app-all\", \"app-hide\", \"app-show\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"indexmap\", \"infer\", \"isolation\", \"linux-protocol-headers\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"nix\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os-api\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"sys-locale\", \"system-tray\", \"tauri-runtime-wry\", \"test\", \"time\", \"tracing\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-closable\", \"window-set-content-protected\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-ignore-cursor-events\", \"window-set-max-size\", \"window-set-maximizable\", \"window-set-min-size\", \"window-set-minimizable\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 12223948975794516716, "profile": 7668817577240348489, "path": 2779529947099847214, "deps": [[40386456601120721, "percent_encoding", false, 1358475850254272052], [1260461579271933187, "serialize_to_javascript", false, 16138265737759006352], [1441306149310335789, "tempfile", false, 8123498871302877135], [3150220818285335163, "url", false, 8956527005242435352], [3722963349756955755, "once_cell", false, 10281967339976405426], [3988549704697787137, "open", false, 17278237371257388478], [4381063397040571828, "webview2_com", false, 7020866980887193561], [4405182208873388884, "http", false, 3917179729382657189], [4450062412064442726, "dirs_next", false, 14395564488083450634], [4899080583175475170, "semver", false, 5038342648670020724], [5180608563399064494, "tauri_macros", false, 11616212555260070863], [5610773616282026064, "build_script_build", false, 4995730829570754763], [5986029879202738730, "log", false, 7349739194943966833], [7653476968652377684, "windows", false, 3713878180079890005], [8008191657135824715, "thiserror", false, 2993888177908691868], [8292277814562636972, "tauri_utils", false, 12257840116341748066], [8319709847752024821, "uuid", false, 15208946729050052671], [8569119365930580996, "serde_json", false, 14354482580527557019], [9451456094439810778, "regex", false, 16255396858535810672], [9623796893764309825, "ignore", false, 10783066607045519446], [9689903380558560274, "serde", false, 2554312950418482273], [9920160576179037441, "getrandom", false, 1868457302755311668], [10629569228670356391, "futures_util", false, 12425151342289843072], [11601763207901161556, "tar", false, 10025671552562191493], [11693073011723388840, "raw_window_handle", false, 15322742465324411442], [11989259058781683633, "dunce", false, 14967221997103913491], [12944427623413450645, "tokio", false, 17110978009406546642], [12986574360607194341, "serde_repr", false, 6493277905820674536], [13208667028893622512, "rand", false, 2943278044707951345], [13625485746686963219, "anyhow", false, 13440987046065721139], [14162324460024849578, "tauri_runtime", false, 10021992693577726700], [14564311161534545801, "encoding_rs", false, 1731774522106950946], [16228250612241359704, "tauri_runtime_wry", false, 18398207868952017465], [17155886227862585100, "glob", false, 8629543466278507445], [17278893514130263345, "state", false, 3306814243625255127], [17772299992546037086, "flate2", false, 6314112702232151921]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-d832a84996ceff0f\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}