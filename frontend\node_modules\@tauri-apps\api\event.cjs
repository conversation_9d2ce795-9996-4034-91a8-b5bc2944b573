"use strict";var u=Object.defineProperty;var v=Object.getOwnPropertyDescriptor;var y=Object.getOwnPropertyNames;var g=Object.prototype.hasOwnProperty;var w=(e,n)=>{for(var r in n)u(e,r,{get:n[r],enumerable:!0})},D=(e,n,r,o)=>{if(n&&typeof n=="object"||typeof n=="function")for(let i of y(n))!g.call(e,i)&&i!==r&&u(e,i,{get:()=>n[i],enumerable:!(o=v(n,i))||o.enumerable});return e};var A=e=>D(u({},"__esModule",{value:!0}),e);var O={};w(O,{TauriEvent:()=>E,emit:()=>N,listen:()=>I,once:()=>b});module.exports=A(O);function C(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function a(e,n=!1){let r=C(),o=`_${r}`;return Object.defineProperty(window,o,{value:i=>(n&&Reflect.deleteProperty(window,o),e?.(i)),writable:!1,configurable:!0}),r}async function m(e,n={}){return new Promise((r,o)=>{let i=a(l=>{r(l),Reflect.deleteProperty(window,`_${d}`)},!0),d=a(l=>{o(l),Reflect.deleteProperty(window,`_${i}`)},!0);window.__TAURI_IPC__({cmd:e,callback:i,error:d,...n})})}async function s(e){return m("tauri",e)}async function p(e,n){return s({__tauriModule:"Event",message:{cmd:"unlisten",event:e,eventId:n}})}async function _(e,n,r){await s({__tauriModule:"Event",message:{cmd:"emit",event:e,windowLabel:n,payload:r}})}async function c(e,n,r){return s({__tauriModule:"Event",message:{cmd:"listen",event:e,windowLabel:n,handler:a(r)}}).then(o=>async()=>p(e,o))}async function f(e,n,r){return c(e,n,o=>{r(o),p(e,o.id).catch(()=>{})})}var E=(t=>(t.WINDOW_RESIZED="tauri://resize",t.WINDOW_MOVED="tauri://move",t.WINDOW_CLOSE_REQUESTED="tauri://close-requested",t.WINDOW_CREATED="tauri://window-created",t.WINDOW_DESTROYED="tauri://destroyed",t.WINDOW_FOCUS="tauri://focus",t.WINDOW_BLUR="tauri://blur",t.WINDOW_SCALE_FACTOR_CHANGED="tauri://scale-change",t.WINDOW_THEME_CHANGED="tauri://theme-changed",t.WINDOW_FILE_DROP="tauri://file-drop",t.WINDOW_FILE_DROP_HOVER="tauri://file-drop-hover",t.WINDOW_FILE_DROP_CANCELLED="tauri://file-drop-cancelled",t.MENU="tauri://menu",t.CHECK_UPDATE="tauri://update",t.UPDATE_AVAILABLE="tauri://update-available",t.INSTALL_UPDATE="tauri://update-install",t.STATUS_UPDATE="tauri://update-status",t.DOWNLOAD_PROGRESS="tauri://update-download-progress",t))(E||{});async function I(e,n){return c(e,null,n)}async function b(e,n){return f(e,null,n)}async function N(e,n){return _(e,void 0,n)}
