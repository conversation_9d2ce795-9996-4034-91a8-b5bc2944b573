"use strict";var a=Object.defineProperty;var p=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var f=Object.prototype.hasOwnProperty;var _=(r,e)=>{for(var o in e)a(r,o,{get:e[o],enumerable:!0})},g=(r,e,o,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of m(e))!f.call(r,n)&&n!==o&&a(r,n,{get:()=>e[n],enumerable:!(i=p(e,n))||i.enumerable});return r};var y=r=>g(a({},"__esModule",{value:!0}),r);var M={};_(M,{EOL:()=>T,arch:()=>x,locale:()=>A,platform:()=>v,tempdir:()=>O,type:()=>b,version:()=>P});module.exports=y(M);function u(){return navigator.appVersion.includes("Win")}function w(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function l(r,e=!1){let o=w(),i=`_${o}`;return Object.defineProperty(window,i,{value:n=>(e&&Reflect.deleteProperty(window,i),r?.(n)),writable:!1,configurable:!0}),o}async function d(r,e={}){return new Promise((o,i)=>{let n=l(s=>{o(s),Reflect.deleteProperty(window,`_${c}`)},!0),c=l(s=>{i(s),Reflect.deleteProperty(window,`_${n}`)},!0);window.__TAURI_IPC__({cmd:r,callback:n,error:c,...e})})}async function t(r){return d("tauri",r)}var T=u()?`\r
`:`
`;async function v(){return t({__tauriModule:"Os",message:{cmd:"platform"}})}async function P(){return t({__tauriModule:"Os",message:{cmd:"version"}})}async function b(){return t({__tauriModule:"Os",message:{cmd:"osType"}})}async function x(){return t({__tauriModule:"Os",message:{cmd:"arch"}})}async function O(){return t({__tauriModule:"Os",message:{cmd:"tempdir"}})}async function A(){return t({__tauriModule:"Os",message:{cmd:"locale"}})}
