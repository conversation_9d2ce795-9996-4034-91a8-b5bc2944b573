import{a as o}from"./chunk-J2IGCSS2.js";import{a as c}from"./chunk-FEIY7W7S.js";var T={};c(T,{Body:()=>a,Client:()=>m,Response:()=>u,ResponseType:()=>y,fetch:()=>f,getClient:()=>l});var y=(r=>(r[r.JSON=1]="JSON",r[r.Text=2]="Text",r[r.Binary=3]="Binary",r))(y||{});async function d(s){let e={},n=async(r,t)=>{if(t!==null){let i;typeof t=="string"?i=t:t instanceof Uint8Array||Array.isArray(t)?i=Array.from(t):t instanceof File?i={file:Array.from(new Uint8Array(await t.arrayBuffer())),mime:t.type,fileName:t.name}:typeof t.file=="string"?i={file:t.file,mime:t.mime,fileName:t.fileName}:i={file:Array.from(t.file),mime:t.mime,fileName:t.fileName},e[String(r)]=i}};if(s instanceof FormData)for(let[r,t]of s)await n(r,t);else for(let[r,t]of Object.entries(s))await n(r,t);return e}var a=class{constructor(e,n){this.type=e,this.payload=n}static form(e){return new a("Form",e)}static json(e){return new a("Json",e)}static text(e){return new a("Text",e)}static bytes(e){return new a("Bytes",Array.from(e instanceof ArrayBuffer?new Uint8Array(e):e))}},u=class{constructor(e){this.url=e.url,this.status=e.status,this.ok=this.status>=200&&this.status<300,this.headers=e.headers,this.rawHeaders=e.rawHeaders,this.data=e.data}},m=class{constructor(e){this.id=e}async drop(){return o({__tauriModule:"Http",message:{cmd:"dropClient",client:this.id}})}async request(e){let n=!e.responseType||e.responseType===1;return n&&(e.responseType=2),e.body?.type==="Form"&&(e.body.payload=await d(e.body.payload)),o({__tauriModule:"Http",message:{cmd:"httpRequest",client:this.id,options:e}}).then(r=>{let t=new u(r);if(n){try{t.data=JSON.parse(t.data)}catch(i){if(t.ok&&t.data==="")t.data={};else if(t.ok)throw Error(`Failed to parse response \`${t.data}\` as JSON: ${i};
              try setting the \`responseType\` option to \`ResponseType.Text\` or \`ResponseType.Binary\` if the API does not return a JSON response.`)}return t}return t})}async get(e,n){return this.request({method:"GET",url:e,...n})}async post(e,n,r){return this.request({method:"POST",url:e,body:n,...r})}async put(e,n,r){return this.request({method:"PUT",url:e,body:n,...r})}async patch(e,n){return this.request({method:"PATCH",url:e,...n})}async delete(e,n){return this.request({method:"DELETE",url:e,...n})}};async function l(s){return o({__tauriModule:"Http",message:{cmd:"createClient",options:s}}).then(e=>new m(e))}var p=null;async function f(s,e){return p===null&&(p=await l()),p.request({url:s,method:e?.method??"GET",...e})}export{y as a,a as b,u as c,m as d,l as e,f,T as g};
