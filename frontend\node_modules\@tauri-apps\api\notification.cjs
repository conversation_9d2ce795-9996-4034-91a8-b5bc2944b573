"use strict";var s=Object.defineProperty;var l=Object.getOwnPropertyDescriptor;var f=Object.getOwnPropertyNames;var m=Object.prototype.hasOwnProperty;var w=(e,i)=>{for(var r in i)s(e,r,{get:i[r],enumerable:!0})},p=(e,i,r,o)=>{if(i&&typeof i=="object"||typeof i=="function")for(let n of f(i))!m.call(e,n)&&n!==r&&s(e,n,{get:()=>i[n],enumerable:!(o=l(i,n))||o.enumerable});return e};var g=e=>p(s({},"__esModule",{value:!0}),e);var T={};w(T,{isPermissionGranted:()=>y,requestPermission:()=>P,sendNotification:()=>v});module.exports=g(T);function _(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function c(e,i=!1){let r=_(),o=`_${r}`;return Object.defineProperty(window,o,{value:n=>(i&&Reflect.deleteProperty(window,o),e?.(n)),writable:!1,configurable:!0}),r}async function d(e,i={}){return new Promise((r,o)=>{let n=c(t=>{r(t),Reflect.deleteProperty(window,`_${a}`)},!0),a=c(t=>{o(t),Reflect.deleteProperty(window,`_${n}`)},!0);window.__TAURI_IPC__({cmd:e,callback:n,error:a,...i})})}async function u(e){return d("tauri",e)}async function y(){return window.Notification.permission!=="default"?Promise.resolve(window.Notification.permission==="granted"):u({__tauriModule:"Notification",message:{cmd:"isNotificationPermissionGranted"}})}async function P(){return window.Notification.requestPermission()}function v(e){typeof e=="string"?new window.Notification(e):new window.Notification(e.title,e)}
