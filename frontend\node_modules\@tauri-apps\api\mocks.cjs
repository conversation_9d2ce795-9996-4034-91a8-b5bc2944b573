"use strict";var r=Object.defineProperty;var t=Object.getOwnPropertyDescriptor;var d=Object.getOwnPropertyNames;var c=Object.prototype.hasOwnProperty;var s=(o,n)=>{for(var _ in n)r(o,_,{get:n[_],enumerable:!0})},a=(o,n,_,i)=>{if(n&&typeof n=="object"||typeof n=="function")for(let e of d(n))!c.call(o,e)&&e!==_&&r(o,e,{get:()=>n[e],enumerable:!(i=t(n,e))||i.enumerable});return o};var A=o=>a(r({},"__esModule",{value:!0}),o);var T={};s(T,{clearMocks:()=>m,mockIPC:()=>g,mockWindows:()=>I});module.exports=A(T);function g(o){window.__TAURI_IPC__=async({cmd:n,callback:_,error:i,...e})=>{try{window[`_${_}`](await o(n,e))}catch(w){window[`_${i}`](w)}}}function I(o,...n){window.__TAURI_METADATA__={__windows:[o,...n].map(_=>({label:_})),__currentWindow:{label:o}}}function m(){delete window.__TAURI_IPC__,delete window.__TAURI_METADATA__}
