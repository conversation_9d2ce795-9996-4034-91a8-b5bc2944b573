import{a as d}from"./chunk-J2IGCSS2.js";import{a as h}from"./chunk-SELMD7YX.js";import{a as u}from"./chunk-FEIY7W7S.js";var m={};u(m,{Child:()=>c,Command:()=>l,EventEmitter:()=>i,open:()=>g});async function p(o,e,t=[],r){return typeof t=="object"&&Object.freeze(t),d({__tauriModule:"Shell",message:{cmd:"execute",program:e,args:t,options:r,onEventFn:h(o)}})}var i=class{constructor(){this.eventListeners=Object.create(null)}addListener(e,t){return this.on(e,t)}removeListener(e,t){return this.off(e,t)}on(e,t){return e in this.eventListeners?this.eventListeners[e].push(t):this.eventListeners[e]=[t],this}once(e,t){let r=(...s)=>{this.removeListener(e,r),t(...s)};return this.addListener(e,r)}off(e,t){return e in this.eventListeners&&(this.eventListeners[e]=this.eventListeners[e].filter(r=>r!==t)),this}removeAllListeners(e){return e?delete this.eventListeners[e]:this.eventListeners=Object.create(null),this}emit(e,...t){if(e in this.eventListeners){let r=this.eventListeners[e];for(let s of r)s(...t);return!0}return!1}listenerCount(e){return e in this.eventListeners?this.eventListeners[e].length:0}prependListener(e,t){return e in this.eventListeners?this.eventListeners[e].unshift(t):this.eventListeners[e]=[t],this}prependOnceListener(e,t){let r=(...s)=>{this.removeListener(e,r),t(...s)};return this.prependListener(e,r)}},c=class{constructor(e){this.pid=e}async write(e){return d({__tauriModule:"Shell",message:{cmd:"stdinWrite",pid:this.pid,buffer:typeof e=="string"?e:Array.from(e)}})}async kill(){return d({__tauriModule:"Shell",message:{cmd:"killChild",pid:this.pid}})}},l=class extends i{constructor(t,r=[],s){super();this.stdout=new i;this.stderr=new i;this.program=t,this.args=typeof r=="string"?[r]:r,this.options=s??{}}static sidecar(t,r=[],s){let a=new l(t,r,s);return a.options.sidecar=!0,a}async spawn(){return p(t=>{switch(t.event){case"Error":this.emit("error",t.payload);break;case"Terminated":this.emit("close",t.payload);break;case"Stdout":this.stdout.emit("data",t.payload);break;case"Stderr":this.stderr.emit("data",t.payload);break}},this.program,this.args,this.options).then(t=>new c(t))}async execute(){return new Promise((t,r)=>{this.on("error",r);let s=[],a=[];this.stdout.on("data",n=>{s.push(n)}),this.stderr.on("data",n=>{a.push(n)}),this.on("close",n=>{t({code:n.code,signal:n.signal,stdout:s.join(`
`),stderr:a.join(`
`)})}),this.spawn().catch(r)})}};async function g(o,e){return d({__tauriModule:"Shell",message:{cmd:"open",path:o,with:e}})}export{i as a,c as b,l as c,g as d,m as e};
