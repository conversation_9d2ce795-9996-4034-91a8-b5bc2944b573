import{a as e}from"./chunk-J2IGCSS2.js";import{a as o}from"./chunk-SELMD7YX.js";import{a as i}from"./chunk-FEIY7W7S.js";var c={};i(c,{isRegistered:()=>u,register:()=>s,registerAll:()=>n,unregister:()=>a,unregisterAll:()=>l});async function s(r,t){return e({__tauriModule:"GlobalShortcut",message:{cmd:"register",shortcut:r,handler:o(t)}})}async function n(r,t){return e({__tauriModule:"GlobalShortcut",message:{cmd:"registerAll",shortcuts:r,handler:o(t)}})}async function u(r){return e({__tauriModule:"GlobalShortcut",message:{cmd:"isRegistered",shortcut:r}})}async function a(r){return e({__tauriModule:"GlobalShortcut",message:{cmd:"unregister",shortcut:r}})}async function l(){return e({__tauriModule:"GlobalShortcut",message:{cmd:"unregisterAll"}})}export{s as a,n as b,u as c,a as d,l as e,c as f};
