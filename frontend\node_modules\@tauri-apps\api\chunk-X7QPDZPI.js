import{a as o}from"./chunk-J2IGCSS2.js";import{a as p}from"./chunk-FEIY7W7S.js";var v={};p(v,{BaseDirectory:()=>F,Dir:()=>F,copyFile:()=>c,createDir:()=>d,exists:()=>b,readBinaryFile:()=>a,readDir:()=>m,readTextFile:()=>l,removeDir:()=>g,removeFile:()=>O,renameFile:()=>_,writeBinaryFile:()=>f,writeFile:()=>u,writeTextFile:()=>u});var F=(n=>(n[n.Audio=1]="Audio",n[n.Cache=2]="Cache",n[n.Config=3]="Config",n[n.Data=4]="Data",n[n.LocalData=5]="LocalData",n[n.Desktop=6]="Desktop",n[n.Document=7]="Document",n[n.Download=8]="Download",n[n.Executable=9]="Executable",n[n.Font=10]="Font",n[n.Home=11]="Home",n[n.Picture=12]="Picture",n[n.Public=13]="Public",n[n.Runtime=14]="Runtime",n[n.Template=15]="Template",n[n.Video=16]="Video",n[n.Resource=17]="Resource",n[n.App=18]="App",n[n.Log=19]="Log",n[n.Temp=20]="Temp",n[n.AppConfig=21]="AppConfig",n[n.AppData=22]="AppData",n[n.AppLocalData=23]="AppLocalData",n[n.AppCache=24]="AppCache",n[n.AppLog=25]="AppLog",n))(F||{});async function l(i,t={}){return o({__tauriModule:"Fs",message:{cmd:"readTextFile",path:i,options:t}})}async function a(i,t={}){let s=await o({__tauriModule:"Fs",message:{cmd:"readFile",path:i,options:t}});return Uint8Array.from(s)}async function u(i,t,s){typeof s=="object"&&Object.freeze(s),typeof i=="object"&&Object.freeze(i);let e={path:"",contents:""},r=s;return typeof i=="string"?e.path=i:(e.path=i.path,e.contents=i.contents),typeof t=="string"?e.contents=t??"":r=t,o({__tauriModule:"Fs",message:{cmd:"writeFile",path:e.path,contents:Array.from(new TextEncoder().encode(e.contents)),options:r}})}async function f(i,t,s){typeof s=="object"&&Object.freeze(s),typeof i=="object"&&Object.freeze(i);let e={path:"",contents:[]},r=s;return typeof i=="string"?e.path=i:(e.path=i.path,e.contents=i.contents),t&&"dir"in t?r=t:typeof i=="string"&&(e.contents=t??[]),o({__tauriModule:"Fs",message:{cmd:"writeFile",path:e.path,contents:Array.from(e.contents instanceof ArrayBuffer?new Uint8Array(e.contents):e.contents),options:r}})}async function m(i,t={}){return o({__tauriModule:"Fs",message:{cmd:"readDir",path:i,options:t}})}async function d(i,t={}){return o({__tauriModule:"Fs",message:{cmd:"createDir",path:i,options:t}})}async function g(i,t={}){return o({__tauriModule:"Fs",message:{cmd:"removeDir",path:i,options:t}})}async function c(i,t,s={}){return o({__tauriModule:"Fs",message:{cmd:"copyFile",source:i,destination:t,options:s}})}async function O(i,t={}){return o({__tauriModule:"Fs",message:{cmd:"removeFile",path:i,options:t}})}async function _(i,t,s={}){return o({__tauriModule:"Fs",message:{cmd:"renameFile",oldPath:i,newPath:t,options:s}})}async function b(i,t={}){return o({__tauriModule:"Fs",message:{cmd:"exists",path:i,options:t}})}export{F as a,l as b,a as c,u as d,f as e,m as f,d as g,g as h,c as i,O as j,_ as k,b as l,v as m};
