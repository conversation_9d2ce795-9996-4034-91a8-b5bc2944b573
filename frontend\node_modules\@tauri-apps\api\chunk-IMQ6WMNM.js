import{a as s}from"./chunk-V5J25SYE.js";import{a as r}from"./chunk-J2IGCSS2.js";import{a as e}from"./chunk-FEIY7W7S.js";var u={};e(u,{EOL:()=>n,arch:()=>a,locale:()=>c,platform:()=>o,tempdir:()=>m,type:()=>t,version:()=>i});var n=s()?`\r
`:`
`;async function o(){return r({__tauriModule:"Os",message:{cmd:"platform"}})}async function i(){return r({__tauriModule:"Os",message:{cmd:"version"}})}async function t(){return r({__tauriModule:"Os",message:{cmd:"osType"}})}async function a(){return r({__tauriModule:"Os",message:{cmd:"arch"}})}async function m(){return r({__tauriModule:"Os",message:{cmd:"tempdir"}})}async function c(){return r({__tauriModule:"Os",message:{cmd:"locale"}})}export{n as a,o as b,i as c,t as d,a as e,m as f,c as g,u as h};
