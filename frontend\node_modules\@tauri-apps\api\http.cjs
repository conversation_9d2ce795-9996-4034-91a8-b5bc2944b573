"use strict";var l=Object.defineProperty;var g=Object.getOwnPropertyDescriptor;var h=Object.getOwnPropertyNames;var w=Object.prototype.hasOwnProperty;var P=(i,e)=>{for(var r in e)l(i,r,{get:e[r],enumerable:!0})},R=(i,e,r,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let t of h(e))!w.call(i,t)&&t!==r&&l(i,t,{get:()=>e[t],enumerable:!(n=g(e,t))||n.enumerable});return i};var b=i=>R(l({},"__esModule",{value:!0}),i);var _={};P(_,{Body:()=>o,Client:()=>m,Response:()=>u,ResponseType:()=>f,fetch:()=>F,getClient:()=>T});module.exports=b(_);function O(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function d(i,e=!1){let r=O(),n=`_${r}`;return Object.defineProperty(window,n,{value:t=>(e&&Reflect.deleteProperty(window,n),i?.(t)),writable:!1,configurable:!0}),r}async function y(i,e={}){return new Promise((r,n)=>{let t=d(c=>{r(c),Reflect.deleteProperty(window,`_${s}`)},!0),s=d(c=>{n(c),Reflect.deleteProperty(window,`_${t}`)},!0);window.__TAURI_IPC__({cmd:i,callback:t,error:s,...e})})}async function a(i){return y("tauri",i)}var f=(n=>(n[n.JSON=1]="JSON",n[n.Text=2]="Text",n[n.Binary=3]="Binary",n))(f||{});async function A(i){let e={},r=async(n,t)=>{if(t!==null){let s;typeof t=="string"?s=t:t instanceof Uint8Array||Array.isArray(t)?s=Array.from(t):t instanceof File?s={file:Array.from(new Uint8Array(await t.arrayBuffer())),mime:t.type,fileName:t.name}:typeof t.file=="string"?s={file:t.file,mime:t.mime,fileName:t.fileName}:s={file:Array.from(t.file),mime:t.mime,fileName:t.fileName},e[String(n)]=s}};if(i instanceof FormData)for(let[n,t]of i)await r(n,t);else for(let[n,t]of Object.entries(i))await r(n,t);return e}var o=class{constructor(e,r){this.type=e,this.payload=r}static form(e){return new o("Form",e)}static json(e){return new o("Json",e)}static text(e){return new o("Text",e)}static bytes(e){return new o("Bytes",Array.from(e instanceof ArrayBuffer?new Uint8Array(e):e))}},u=class{constructor(e){this.url=e.url,this.status=e.status,this.ok=this.status>=200&&this.status<300,this.headers=e.headers,this.rawHeaders=e.rawHeaders,this.data=e.data}},m=class{constructor(e){this.id=e}async drop(){return a({__tauriModule:"Http",message:{cmd:"dropClient",client:this.id}})}async request(e){let r=!e.responseType||e.responseType===1;return r&&(e.responseType=2),e.body?.type==="Form"&&(e.body.payload=await A(e.body.payload)),a({__tauriModule:"Http",message:{cmd:"httpRequest",client:this.id,options:e}}).then(n=>{let t=new u(n);if(r){try{t.data=JSON.parse(t.data)}catch(s){if(t.ok&&t.data==="")t.data={};else if(t.ok)throw Error(`Failed to parse response \`${t.data}\` as JSON: ${s};
              try setting the \`responseType\` option to \`ResponseType.Text\` or \`ResponseType.Binary\` if the API does not return a JSON response.`)}return t}return t})}async get(e,r){return this.request({method:"GET",url:e,...r})}async post(e,r,n){return this.request({method:"POST",url:e,body:r,...n})}async put(e,r,n){return this.request({method:"PUT",url:e,body:r,...n})}async patch(e,r){return this.request({method:"PATCH",url:e,...r})}async delete(e,r){return this.request({method:"DELETE",url:e,...r})}};async function T(i){return a({__tauriModule:"Http",message:{cmd:"createClient",options:i}}).then(e=>new m(e))}var p=null;async function F(i,e){return p===null&&(p=await T()),p.request({url:i,method:e?.method??"GET",...e})}
