import{a as e}from"./chunk-V5J25SYE.js";import{a as i}from"./chunk-X7QPDZPI.js";import{a as r}from"./chunk-J2IGCSS2.js";import{a as o}from"./chunk-FEIY7W7S.js";var q={};o(q,{BaseDirectory:()=>i,appCacheDir:()=>g,appConfigDir:()=>s,appDataDir:()=>c,appDir:()=>u,appLocalDataDir:()=>m,appLogDir:()=>n,audioDir:()=>d,basename:()=>V,cacheDir:()=>P,configDir:()=>h,dataDir:()=>l,delimiter:()=>z,desktopDir:()=>_,dirname:()=>F,documentDir:()=>p,downloadDir:()=>y,executableDir:()=>f,extname:()=>H,fontDir:()=>D,homeDir:()=>M,isAbsolute:()=>W,join:()=>E,localDataDir:()=>v,logDir:()=>w,normalize:()=>B,pictureDir:()=>b,publicDir:()=>A,resolve:()=>T,resolveResource:()=>x,resourceDir:()=>C,runtimeDir:()=>L,sep:()=>j,templateDir:()=>R,videoDir:()=>k});async function u(){return s()}async function s(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:21}})}async function c(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:22}})}async function m(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:23}})}async function g(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:24}})}async function d(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:1}})}async function P(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:2}})}async function h(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:3}})}async function l(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:4}})}async function _(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:6}})}async function p(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:7}})}async function y(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:8}})}async function f(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:9}})}async function D(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:10}})}async function M(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:11}})}async function v(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:5}})}async function b(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:12}})}async function A(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:13}})}async function C(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:17}})}async function x(t){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:t,directory:17}})}async function L(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:14}})}async function R(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:15}})}async function k(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:16}})}async function w(){return n()}async function n(){return r({__tauriModule:"Path",message:{cmd:"resolvePath",path:"",directory:25}})}var j=e()?"\\":"/",z=e()?";":":";async function T(...t){return r({__tauriModule:"Path",message:{cmd:"resolve",paths:t}})}async function B(t){return r({__tauriModule:"Path",message:{cmd:"normalize",path:t}})}async function E(...t){return r({__tauriModule:"Path",message:{cmd:"join",paths:t}})}async function F(t){return r({__tauriModule:"Path",message:{cmd:"dirname",path:t}})}async function H(t){return r({__tauriModule:"Path",message:{cmd:"extname",path:t}})}async function V(t,a){return r({__tauriModule:"Path",message:{cmd:"basename",path:t,ext:a}})}async function W(t){return r({__tauriModule:"Path",message:{cmd:"isAbsolute",path:t}})}export{u as a,s as b,c,m as d,g as e,d as f,P as g,h,l as i,_ as j,p as k,y as l,f as m,D as n,M as o,v as p,b as q,A as r,C as s,x as t,L as u,R as v,k as w,w as x,n as y,j as z,z as A,T as B,B as C,E as D,F as E,H as F,V as G,W as H,q as I};
