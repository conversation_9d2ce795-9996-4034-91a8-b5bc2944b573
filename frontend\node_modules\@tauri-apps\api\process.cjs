"use strict";var a=Object.defineProperty;var d=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var _=Object.prototype.hasOwnProperty;var p=(e,r)=>{for(var o in r)a(e,o,{get:r[o],enumerable:!0})},f=(e,r,o,t)=>{if(r&&typeof r=="object"||typeof r=="function")for(let n of m(r))!_.call(e,n)&&n!==o&&a(e,n,{get:()=>r[n],enumerable:!(t=d(r,n))||t.enumerable});return e};var g=e=>f(a({},"__esModule",{value:!0}),e);var T={};p(T,{exit:()=>y,relaunch:()=>v});module.exports=g(T);function w(){return window.crypto.getRandomValues(new Uint32Array(1))[0]}function c(e,r=!1){let o=w(),t=`_${o}`;return Object.defineProperty(window,t,{value:n=>(r&&Reflect.deleteProperty(window,t),e?.(n)),writable:!1,configurable:!0}),o}async function l(e,r={}){return new Promise((o,t)=>{let n=c(i=>{o(i),Reflect.deleteProperty(window,`_${u}`)},!0),u=c(i=>{t(i),Reflect.deleteProperty(window,`_${n}`)},!0);window.__TAURI_IPC__({cmd:e,callback:n,error:u,...r})})}async function s(e){return l("tauri",e)}async function y(e=0){return s({__tauriModule:"Process",message:{cmd:"exit",exitCode:e}})}async function v(){return s({__tauriModule:"Process",message:{cmd:"relaunch"}})}
