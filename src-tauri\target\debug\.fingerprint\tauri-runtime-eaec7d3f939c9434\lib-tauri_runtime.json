{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 7668817577240348489, "path": 1339320275069559873, "deps": [[3150220818285335163, "url", false, 8956527005242435352], [4381063397040571828, "webview2_com", false, 7020866980887193561], [4405182208873388884, "http", false, 3917179729382657189], [7653476968652377684, "windows", false, 3713878180079890005], [8008191657135824715, "thiserror", false, 2993888177908691868], [8292277814562636972, "tauri_utils", false, 12257840116341748066], [8319709847752024821, "uuid", false, 15208946729050052671], [8569119365930580996, "serde_json", false, 14354482580527557019], [8866577183823226611, "http_range", false, 5969744606292815347], [9689903380558560274, "serde", false, 2554312950418482273], [11693073011723388840, "raw_window_handle", false, 15322742465324411442], [13208667028893622512, "rand", false, 2943278044707951345], [14162324460024849578, "build_script_build", false, 16636897965233613229]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-eaec7d3f939c9434\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}