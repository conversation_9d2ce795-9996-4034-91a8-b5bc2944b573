import{a as n}from"./chunk-J2IGCSS2.js";import{a as e}from"./chunk-FEIY7W7S.js";var r={};e(r,{isPermissionGranted:()=>t,requestPermission:()=>o,sendNotification:()=>s});async function t(){return window.Notification.permission!=="default"?Promise.resolve(window.Notification.permission==="granted"):n({__tauriModule:"Notification",message:{cmd:"isNotificationPermissionGranted"}})}async function o(){return window.Notification.requestPermission()}function s(i){typeof i=="string"?new window.Notification(i):new window.Notification(i.title,i)}export{t as a,o as b,s as c,r as d};
