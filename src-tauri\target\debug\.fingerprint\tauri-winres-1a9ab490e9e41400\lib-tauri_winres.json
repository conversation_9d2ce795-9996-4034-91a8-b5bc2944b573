{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 10555948925697328552, "profile": 2225463790103693989, "path": 6153130744623688991, "deps": [[1293861355733423614, "toml", false, 9473086986336934385], [11501392865286155686, "embed_resource", false, 11183084955159443048]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winres-1a9ab490e9e41400\\dep-lib-tauri_winres", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}