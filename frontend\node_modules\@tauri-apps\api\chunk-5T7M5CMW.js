import{a as n}from"./chunk-J2IGCSS2.js";import{a as o}from"./chunk-FEIY7W7S.js";var c={};o(c,{ask:()=>l,confirm:()=>g,message:()=>s,open:()=>a,save:()=>r});async function a(t={}){return typeof t=="object"&&Object.freeze(t),n({__tauriModule:"Dialog",message:{cmd:"openDialog",options:t}})}async function r(t={}){return typeof t=="object"&&Object.freeze(t),n({__tauriModule:"Dialog",message:{cmd:"saveDialog",options:t}})}async function s(t,i){let e=typeof i=="string"?{title:i}:i;return n({__tauriModule:"Dialog",message:{cmd:"messageDialog",message:t.toString(),title:e?.title?.toString(),type:e?.type,buttonLabel:e?.okLabel?.toString()}})}async function l(t,i){let e=typeof i=="string"?{title:i}:i;return n({__tauriModule:"Dialog",message:{cmd:"askDialog",message:t.toString(),title:e?.title?.toString(),type:e?.type,buttonLabels:[e?.okLabel?.toString()??"Yes",e?.cancelLabel?.toString()??"No"]}})}async function g(t,i){let e=typeof i=="string"?{title:i}:i;return n({__tauriModule:"Dialog",message:{cmd:"confirmDialog",message:t.toString(),title:e?.title?.toString(),type:e?.type,buttonLabels:[e?.okLabel?.toString()??"Ok",e?.cancelLabel?.toString()??"Cancel"]}})}export{a,r as b,s as c,l as d,g as e,c as f};
