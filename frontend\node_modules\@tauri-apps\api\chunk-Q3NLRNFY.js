import{e as u,f as p,g as s}from"./chunk-5NWA45Z3.js";import{a as U}from"./chunk-FEIY7W7S.js";var l={};U(l,{checkUpdate:()=>c,installUpdate:()=>f,onUpdaterEvent:()=>d});async function d(n){return u("tauri://update-status",e=>{n(e?.payload)})}async function f(){let n;function e(){n&&n(),n=void 0}return new Promise((i,r)=>{function o(a){if(a.error){e(),r(a.error);return}a.status==="DONE"&&(e(),i())}d(o).then(a=>{n=a}).catch(a=>{throw e(),a}),s("tauri://update-install").catch(a=>{throw e(),a})})}async function c(){let n;function e(){n&&n(),n=void 0}return new Promise((i,r)=>{function o(t){e(),i({manifest:t,shouldUpdate:!0})}function a(t){if(t.error){e(),r(t.error);return}t.status==="UPTODATE"&&(e(),i({shouldUpdate:!1}))}p("tauri://update-available",t=>{o(t?.payload)}).catch(t=>{throw e(),t}),d(a).then(t=>{n=t}).catch(t=>{throw e(),t}),s("tauri://update").catch(t=>{throw e(),t})})}export{d as a,f as b,c,l as d};
